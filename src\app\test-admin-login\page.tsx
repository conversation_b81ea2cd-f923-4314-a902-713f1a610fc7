'use client';

import React, { useState } from 'react';
import { signInWithEmailAndPassword } from 'firebase/auth';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';

export default function TestAdminLogin() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testLogin = async () => {
    setLoading(true);
    setResult('');
    
    try {
      const email = '<EMAIL>';
      const password = '123456';
      
      setResult(prev => prev + `🔐 Testing login with ${email}...\n`);
      
      // Try to sign in
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      
      setResult(prev => prev + `✅ Firebase Auth successful! UID: ${user.uid}\n`);
      setResult(prev => prev + `📧 Email: ${user.email}\n`);
      setResult(prev => prev + `✅ Email Verified: ${user.emailVerified}\n`);
      
      // Check user document
      const userDocRef = doc(db, 'users', user.uid);
      const userDoc = await getDoc(userDocRef);
      
      if (userDoc.exists()) {
        const userData = userDoc.data();
        setResult(prev => prev + `📄 User document exists\n`);
        setResult(prev => prev + `👤 Role: ${userData.role}\n`);
        setResult(prev => prev + `📝 Name: ${userData.name}\n`);
        
        // Ensure admin role
        if (userData.role !== 'admin') {
          setResult(prev => prev + `🔄 Updating role to admin...\n`);
          await setDoc(userDocRef, {
            role: 'admin',
            name: 'John Lloyd Callao',
            isActive: true,
            isVerified: true,
            updatedAt: new Date()
          }, { merge: true });
          setResult(prev => prev + `✅ Role updated to admin\n`);
        }
      } else {
        setResult(prev => prev + `📝 Creating user document...\n`);
        const userData = {
          uid: user.uid,
          email: user.email,
          name: 'John Lloyd Callao',
          role: 'admin',
          isActive: true,
          isVerified: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        await setDoc(userDocRef, userData);
        setResult(prev => prev + `✅ User document created\n`);
      }
      
      // Check admin document
      const adminDocRef = doc(db, 'admins', user.uid);
      const adminDoc = await getDoc(adminDocRef);
      
      if (!adminDoc.exists()) {
        setResult(prev => prev + `👑 Creating admin document...\n`);
        const adminData = {
          userRef: `users/${user.uid}`,
          employeeId: 'ADMIN-001',
          fullName: 'John Lloyd Callao',
          department: 'technical',
          accessLevel: 'super_admin',
          permissions: [
            'manage_vendors',
            'handle_disputes',
            'view_analytics',
            'driver_verification',
            'system_config',
            'manage_admins',
            'manage_customers'
          ],
          assignedRegions: ['US', 'CA'],
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        await setDoc(adminDocRef, adminData);
        setResult(prev => prev + `✅ Admin document created\n`);
      } else {
        setResult(prev => prev + `👑 Admin document exists\n`);
      }
      
      setResult(prev => prev + `\n🎉 SUCCESS! Admin login is working!\n`);
      setResult(prev => prev + `\n🚀 You can now login at: /admin-login\n`);
      setResult(prev => prev + `📧 Email: ${email}\n`);
      setResult(prev => prev + `🔑 Password: ${password}\n`);
      
    } catch (error: any) {
      setResult(prev => prev + `❌ Error: ${error.message}\n`);
      setResult(prev => prev + `🔍 Error Code: ${error.code}\n`);
      
      if (error.code === 'auth/user-not-found') {
        setResult(prev => prev + `\n💡 User not found. The email might not be registered.\n`);
      } else if (error.code === 'auth/wrong-password') {
        setResult(prev => prev + `\n💡 Wrong password. The password might be different.\n`);
      } else if (error.code === 'auth/invalid-credential') {
        setResult(prev => prev + `\n💡 Invalid credentials. Email or password is incorrect.\n`);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            🧪 Admin Login Test
          </h1>
          
          <p className="text-gray-600 mb-6">
            This page tests the admin login functionality with the credentials:
            <br />
            <strong>Email:</strong> <EMAIL>
            <br />
            <strong>Password:</strong> 123456
          </p>
          
          <button
            onClick={testLogin}
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed mb-6"
          >
            {loading ? 'Testing...' : 'Test Admin Login'}
          </button>
          
          {result && (
            <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm whitespace-pre-wrap">
              {result}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
