#!/usr/bin/env node

/**
 * Set Firebase Admin Custom Claims for <PERSON>
 * 
 * This script sets the custom claims needed for admin authentication
 */

const admin = require('firebase-admin');

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert({
      projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
      clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    }),
  });
}

const ADMIN_EMAIL = 'john<PERSON><EMAIL>';

async function setAdminClaims() {
  try {
    console.log('🔧 Setting Firebase Admin Custom Claims...');
    console.log(`📧 Email: ${ADMIN_EMAIL}`);
    
    // Get user by email
    const userRecord = await admin.auth().getUserByEmail(ADMIN_EMAIL);
    console.log(`👤 Found user with UID: ${userRecord.uid}`);
    
    // Set custom claims
    const customClaims = {
      role: 'admin',
      accessLevel: 'super_admin',
      permissions: [
        'manage_vendors',
        'handle_disputes',
        'view_analytics',
        'driver_verification',
        'system_config',
        'manage_admins',
        'manage_customers'
      ]
    };
    
    await admin.auth().setCustomUserClaims(userRecord.uid, customClaims);
    console.log('✅ Custom claims set successfully');
    
    // Verify the claims
    const updatedUser = await admin.auth().getUser(userRecord.uid);
    console.log('🔍 Verification - Custom Claims:', updatedUser.customClaims);
    
    console.log('\n🎉 ADMIN CLAIMS SET SUCCESSFULLY!');
    console.log('\n👑 Admin Details:');
    console.log(`- UID: ${userRecord.uid}`);
    console.log(`- Email: ${userRecord.email}`);
    console.log(`- Email Verified: ${userRecord.emailVerified}`);
    console.log(`- Role: ${updatedUser.customClaims?.role}`);
    console.log(`- Access Level: ${updatedUser.customClaims?.accessLevel}`);
    
    console.log('\n🚀 LOGIN INSTRUCTIONS:');
    console.log('1. Open: http://localhost:3000/admin-login');
    console.log(`2. Email: ${ADMIN_EMAIL}`);
    console.log('3. Password: 123456');
    console.log('4. You should now be able to login successfully');
    
    console.log('\n✅ Admin authentication is now properly configured!');
    
  } catch (error) {
    console.error('❌ Error setting admin claims:', error.message);
    
    if (error.code === 'auth/user-not-found') {
      console.log('\n💡 User not found. Please run the create-john-admin.js script first:');
      console.log('node scripts/create-john-admin.js');
    } else if (error.message.includes('Firebase Admin not initialized')) {
      console.log('\n💡 Firebase Admin not initialized. Please check your environment variables:');
      console.log('- FIREBASE_ADMIN_PROJECT_ID');
      console.log('- FIREBASE_ADMIN_CLIENT_EMAIL');
      console.log('- FIREBASE_ADMIN_PRIVATE_KEY');
    }
    
    console.error('Full error:', error);
  }
}

// Run the script
setAdminClaims();
